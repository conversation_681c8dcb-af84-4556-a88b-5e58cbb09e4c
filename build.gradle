plugins {
	id 'java'
	id 'org.springframework.boot' version '3.5.3'
	id 'io.spring.dependency-management' version '1.1.7'
}

group = 'com.gianghp'
version = '0.0.1-SNAPSHOT'

java {
	toolchain {
		languageVersion = JavaLanguageVersion.of(21)
	}
}

configurations {
	compileOnly {
		extendsFrom annotationProcessor
	}
}

repositories {
	mavenCentral()
}

dependencies {
	// Spring Boot starters
	implementation 'org.apache.commons:commons-lang3:3.13.0'
	implementation 'org.springdoc:springdoc-openapi-starter-webmvc-api:2.5.0'
	implementation 'org.springframework.boot:spring-boot-starter-data-jpa'
	implementation 'org.springframework.boot:spring-boot-starter-web'
	implementation 'org.springframework.boot:spring-boot-starter-security'
	implementation 'org.springframework.boot:spring-boot-starter-validation'
	implementation 'org.apache.kafka:kafka-clients:4.0.0'
	implementation 'org.springframework.kafka:spring-kafka'
	implementation 'com.fasterxml.jackson.core:jackson-databind'

	// JWT dependencies
	implementation 'io.jsonwebtoken:jjwt-api:0.12.6'
	runtimeOnly 'io.jsonwebtoken:jjwt-impl:0.12.6'
	runtimeOnly 'io.jsonwebtoken:jjwt-jackson:0.12.6'

	// Lombok
	compileOnly 'org.projectlombok:lombok'
	annotationProcessor 'org.projectlombok:lombok'

	// Development
	developmentOnly 'org.springframework.boot:spring-boot-devtools'

	// Testing
	testImplementation 'org.springframework.boot:spring-boot-starter-test'
	testRuntimeOnly 'org.junit.platform:junit-platform-launcher'
}

tasks.named('test') {
	useJUnitPlatform()
	enabled = false
}

apply plugin: 'maven-publish'

publishing {
	publications {
		gpr(MavenPublication) {
			from components.java
			groupId = project.group        // com.gianghp
			artifactId = 'hrm-common'      // ← Tên package bạn muốn publish
			version = project.version      // 0.0.1-SNAPSHOT hoặc 1.0.0
		}
	}

	repositories {
		maven {
			name = "GitHubPackages"
			url = uri("https://maven.pkg.github.com/gianghp123/hrm-system")
			credentials {
				username = project.findProperty("gpr.user") ?: System.getenv("USERNAME")
				password = project.findProperty("gpr.key") ?: System.getenv("GITHUB_TOKEN")
			}
		}
	}
}